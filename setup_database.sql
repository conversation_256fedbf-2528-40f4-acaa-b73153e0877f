-- PostgreSQL Database Setup Script for INGRES ChatBot
-- Run this script after installing PostgreSQL

-- Create user
CREATE USER ingres_user WITH PASSWORD 'ingres_password';

-- Create database
CREATE DATABASE ingres_db OWNER ingres_user;

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE ingres_db TO ingres_user;

-- Connect to the new database
\c ingres_db;

-- <PERSON> schema privileges
GRANT ALL ON SCHEMA public TO ingres_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ingres_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ingres_user;

-- Create extension for UUID generation (if needed)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Verify setup
SELECT current_database(), current_user;
