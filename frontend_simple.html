<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>INGRES ChatBot - Simple Text Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .chat-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 600px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background: #e9ecef;
            color: #333;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #eee;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        #messageInput {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            resize: vertical;
            min-height: 45px;
            max-height: 120px;
        }

        #messageInput:focus {
            border-color: #007bff;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .status {
            padding: 8px 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 14px;
            text-align: center;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.hidden {
            display: none;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .data-display {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .data-display h4 {
            margin-bottom: 10px;
            color: #007bff;
        }

        .csv-data {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: white;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .message {
                max-width: 95%;
            }
            
            .input-group {
                flex-direction: column;
                gap: 10px;
            }
            
            #messageInput {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 INGRES ChatBot</h1>
            <p>AI-Powered Groundwater Resource Analysis with Voice Support</p>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <strong>Assistant:</strong> Hello! I'm your INGRES AI assistant. I can help you analyze groundwater resources. Ask me questions about groundwater data, recharge rates, state comparisons, and more!
                </div>
            </div>

            <div class="status hidden" id="statusBar"></div>

            <div class="input-container">
                <div class="controls">
                    <button class="btn btn-secondary" onclick="createNewSession()">🆕 New Session</button>
                    <button class="btn btn-secondary" onclick="clearChat()">🗑️ Clear Chat</button>
                    <button class="btn btn-secondary" onclick="toggleInputType()">🎤 Voice Input</button>
                </div>
                
                <!-- Text Input -->
                <div id="textInputGroup" class="input-group">
                    <textarea 
                        id="messageInput" 
                        placeholder="Type your question about groundwater resources..."
                        onkeypress="handleKeyPress(event)"
                        rows="1"
                    ></textarea>
                    <button class="btn btn-primary" onclick="sendMessage()">Send</button>
                </div>
                
                <!-- Voice Input -->
                <div id="voiceInputGroup" class="input-group" style="display: none;">
                    <button id="recordBtn" class="btn btn-primary" onclick="toggleRecording()">
                        🎤 Start Recording
                    </button>
                    <span id="recordingStatus"></span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        let isRecording = false;
        let mediaRecorder = null;
        let recordedChunks = [];
        let currentInputType = 'text'; // 'text' or 'voice'
        const API_BASE_URL = 'http://localhost:8001';

        // Auto-expand textarea
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Toggle between text and voice input
        function toggleInputType() {
            const textGroup = document.getElementById('textInputGroup');
            const voiceGroup = document.getElementById('voiceInputGroup');
            
            if (currentInputType === 'text') {
                currentInputType = 'voice';
                textGroup.style.display = 'none';
                voiceGroup.style.display = 'flex';
            } else {
                currentInputType = 'text';
                textGroup.style.display = 'flex';
                voiceGroup.style.display = 'none';
            }
        }

        // Handle voice recording
        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                await stopRecording();
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                recordedChunks = [];

                mediaRecorder.addEventListener('dataavailable', event => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                });

                mediaRecorder.addEventListener('stop', async () => {
                    const audioBlob = new Blob(recordedChunks, { type: 'audio/wav' });
                    await sendVoiceMessage(audioBlob);
                });

                mediaRecorder.start();
                isRecording = true;
                
                document.getElementById('recordBtn').textContent = '🔴 Stop Recording';
                document.getElementById('recordingStatus').textContent = 'Recording...';
                showStatus('Recording... Click stop when finished', 'info');
                
            } catch (error) {
                showStatus('Error accessing microphone: ' + error.message, 'error');
            }
        }

        async function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                
                document.getElementById('recordBtn').textContent = '🎤 Start Recording';
                document.getElementById('recordingStatus').textContent = '';
                showStatus('Processing voice input...', 'info');
                
                // Stop all tracks to release the microphone
                if (mediaRecorder.stream) {
                    mediaRecorder.stream.getTracks().forEach(track => track.stop());
                }
            }
        }

        // Send voice message
        async function sendVoiceMessage(audioBlob) {
            try {
                // Convert audio to base64
                const reader = new FileReader();
                reader.onloadend = async () => {
                    const base64Audio = reader.result.split(',')[1]; // Remove data:audio/wav;base64, prefix
                    
                    // Add user message to chat
                    addMessage('user', '🎤 Voice message sent');
                    
                    const requestData = {
                        audio_data: base64Audio,
                        input_type: 'voice',
                        session_id: currentSessionId,
                        include_visualization: false
                    };
                    
                    await sendChatRequest(requestData);
                };
                reader.readAsDataURL(audioBlob);
                
            } catch (error) {
                showStatus('Error processing voice input: ' + error.message, 'error');
            }
        }

        // Handle Enter key press
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // Show status message
        function showStatus(message, type = 'info') {
            const statusBar = document.getElementById('statusBar');
            statusBar.textContent = message;
            statusBar.className = `status ${type}`;
            statusBar.classList.remove('hidden');
        }

        // Hide status message
        function hideStatus() {
            const statusBar = document.getElementById('statusBar');
            statusBar.classList.add('hidden');
        }

        // Add message to chat
        function addMessage(sender, content, isHtml = false) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            if (isHtml) {
                messageDiv.innerHTML = content;
            } else {
                messageDiv.textContent = content;
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Create new chat session
        async function createNewSession() {
            try {
                showStatus('Creating new session...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/chat/new-session`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentSessionId = data.session_id;
                    showStatus('New session created successfully', 'success');
                    setTimeout(() => hideStatus(), 2000);
                } else {
                    showStatus('Failed to create session: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                showStatus('Error creating session: ' + error.message, 'error');
            }
        }

        // Clear chat messages
        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="message assistant">
                    <strong>Assistant:</strong> Hello! I'm your INGRES AI assistant. I can help you analyze groundwater resources. Ask me questions about groundwater data, recharge rates, state comparisons, and more!
                </div>
            `;
        }

        // Send text message
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const question = messageInput.value.trim();
            
            if (!question) return;
            
            // Add user message to chat
            addMessage('user', question);
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            const requestData = {
                question: question,
                input_type: 'text',
                session_id: currentSessionId,
                include_visualization: false
            };
            
            await sendChatRequest(requestData);
        }

        // Send chat request (common function for text and voice)
        async function sendChatRequest(requestData) {
            try {
                showStatus('Processing your request...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Update session ID if this was a new session
                    if (data.session_id) {
                        currentSessionId = data.session_id;
                    }
                    
                    // Display the response
                    let responseContent = `<strong>Assistant:</strong> `;
                    
                    // Use translated response for voice input, regular response for text
                    if (data.input_type === 'voice' && data.translated_response) {
                        responseContent += data.translated_response;
                        
                        // Play audio response if available
                        if (data.audio_response) {
                            playAudioResponse(data.audio_response);
                        }
                        
                        // Also show original English response
                        if (data.response !== data.translated_response) {
                            responseContent += `<br><br><strong>English:</strong> ${data.response}`;
                        }
                    } else {
                        responseContent += data.response;
                    }
                    
                    // Add explanation if available
                    if (data.explanation && data.explanation !== data.response && data.explanation !== data.translated_response) {
                        responseContent += `<br><br><strong>Explanation:</strong> ${data.explanation}`;
                    }
                    
                    // Add detected language info for voice input
                    if (data.input_type === 'voice' && data.detected_language) {
                        responseContent += `<br><br><small>🎤 Detected language: ${data.detected_language}</small>`;
                    }
                    
                    // Add data display if available
                    if (data.data && data.data.length > 0) {
                        responseContent += `
                            <div class="data-display">
                                <h4>📊 Query Results (${data.data.length} rows)</h4>
                                <strong>SQL Query:</strong> <code>${data.sql_query}</code>
                        `;
                        
                        if (data.csv_data) {
                            responseContent += `
                                <br><br><strong>Data (CSV format):</strong>
                                <div class="csv-data">${data.csv_data}</div>
                            `;
                        }
                        
                        responseContent += `</div>`;
                    }
                    
                    addMessage('assistant', responseContent, true);
                    hideStatus();
                } else {
                    const errorMessage = data.error || 'An unknown error occurred';
                    addMessage('assistant', `❌ Error: ${errorMessage}`);
                    showStatus('Request failed', 'error');
                }
                
            } catch (error) {
                addMessage('assistant', `❌ Network error: ${error.message}`);
                showStatus('Network error occurred', 'error');
            }
        }

        // Play audio response
        function playAudioResponse(base64Audio) {
            try {
                const audioBlob = new Blob([Uint8Array.from(atob(base64Audio), c => c.charCodeAt(0))], { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);
                
                audio.play().then(() => {
                    showStatus('🔊 Playing audio response', 'success');
                    setTimeout(() => hideStatus(), 2000);
                }).catch(error => {
                    console.error('Error playing audio:', error);
                });
                
                // Clean up URL after playing
                audio.addEventListener('ended', () => {
                    URL.revokeObjectURL(audioUrl);
                });
                
            } catch (error) {
                console.error('Error creating audio from base64:', error);
            }
        }

        // Initialize the application
        window.addEventListener('load', function() {
            // Create initial session
            createNewSession();
        });
    </script>
</body>
</html>