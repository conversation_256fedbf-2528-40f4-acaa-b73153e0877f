# Virtual environments
myEnv/
venv/
env/
.venv/

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Local development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
out/

# Dependencies (should be installed from requirements.txt)
node_modules/

# Git
.git/
.gitignore

# Documentation
README.md
*.md

# Test files
test/
tests/
*_test.py
test_*.py

# Development tools
.pytest_cache/
.coverage
htmlcov/

# Jupyter notebooks
*.ipynb

# Data files (if large)
datasets/*.xlsx
datasets/*.csv

# Temporary files
*.tmp
*.temp
.cache/

# Frontend files (if not needed for API)
frontend_simple.html
frontend_voice.html

# Development scripts
setup_*.py
cli.py
streamlit_app.py
speech-to-text.py
text-to-speech.py
text-to-text.py

# Vercel
.vercel