#!/usr/bin/env python3
"""
Database Setup and Fix <PERSON>ript for INGRES ChatBot
This script helps fix database connection issues and set up the database.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
from dotenv import load_dotenv

def check_postgresql_installed():
    """Check if PostgreSQL is installed on the system"""
    try:
        result = subprocess.run(['psql', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ PostgreSQL found: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ PostgreSQL not found on system")
    return False

def check_postgresql_running():
    """Check if PostgreSQL service is running"""
    system = platform.system().lower()
    
    try:
        if system == "windows":
            result = subprocess.run(['sc', 'query', 'postgresql-x64-16'], capture_output=True, text=True)
            if "RUNNING" in result.stdout:
                print("✅ PostgreSQL service is running")
                return True
        elif system in ["linux", "darwin"]:
            result = subprocess.run(['pg_isready'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ PostgreSQL service is running")
                return True
    except FileNotFoundError:
        pass
    
    print("❌ PostgreSQL service is not running")
    return False

def setup_sqlite_fallback():
    """Set up SQLite as fallback database"""
    print("\n🔄 Setting up SQLite fallback...")
    
    # Update .env file
    env_path = Path('.env')
    if env_path.exists():
        with open(env_path, 'r') as f:
            content = f.read()
        
        # Add or update USE_SQLITE setting
        if 'USE_SQLITE=' in content:
            content = content.replace('USE_SQLITE=false', 'USE_SQLITE=true')
        else:
            content = 'USE_SQLITE=true\nSQLITE_DB_PATH=ingres_db.sqlite\n\n' + content
        
        with open(env_path, 'w') as f:
            f.write(content)
        
        print("✅ Updated .env file to use SQLite")
    else:
        print("❌ .env file not found")
        return False
    
    return True

def test_database_connection():
    """Test database connection"""
    print("\n🔄 Testing database connection...")
    
    try:
        from database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        print("✅ Database connection successful!")
        
        # Test basic functionality
        tables = db_manager.get_table_names()
        print(f"📊 Found {len(tables)} tables in database")
        
        db_manager.close_connection()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def install_postgresql_instructions():
    """Provide instructions for installing PostgreSQL"""
    system = platform.system().lower()
    
    print("\n📋 PostgreSQL Installation Instructions:")
    print("=" * 50)
    
    if system == "windows":
        print("1. Download PostgreSQL from: https://www.postgresql.org/download/windows/")
        print("2. Run the installer and follow the setup wizard")
        print("3. Remember the password you set for the 'postgres' user")
        print("4. After installation, run this script again")
        print("\nAlternatively, use Docker:")
        print("   docker-compose up -d postgres")
    
    elif system == "darwin":  # macOS
        print("Using Homebrew:")
        print("   brew install postgresql@16")
        print("   brew services start postgresql@16")
        print("\nAlternatively, use Docker:")
        print("   docker-compose up -d postgres")
    
    elif system == "linux":
        print("Ubuntu/Debian:")
        print("   sudo apt update")
        print("   sudo apt install postgresql postgresql-contrib")
        print("   sudo systemctl start postgresql")
        print("\nCentOS/RHEL:")
        print("   sudo yum install postgresql-server postgresql-contrib")
        print("   sudo postgresql-setup initdb")
        print("   sudo systemctl start postgresql")
        print("\nAlternatively, use Docker:")
        print("   docker-compose up -d postgres")

def main():
    """Main function to diagnose and fix database issues"""
    print("🔍 INGRES ChatBot Database Diagnostic Tool")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check PostgreSQL installation
    postgres_installed = check_postgresql_installed()
    postgres_running = False
    
    if postgres_installed:
        postgres_running = check_postgresql_running()
    
    # Determine the best course of action
    if postgres_installed and postgres_running:
        print("\n✅ PostgreSQL is installed and running")
        print("🔄 Testing connection...")
        
        if test_database_connection():
            print("\n🎉 Everything is working correctly!")
            return
        else:
            print("\n⚠️  PostgreSQL is running but connection failed")
            print("This might be a configuration issue.")
    
    # PostgreSQL not available, offer solutions
    print("\n🔧 Available Solutions:")
    print("1. Install PostgreSQL (recommended for production)")
    print("2. Use SQLite fallback (quick start)")
    print("3. Use Docker PostgreSQL")
    
    choice = input("\nChoose an option (1/2/3): ").strip()
    
    if choice == "1":
        install_postgresql_instructions()
    elif choice == "2":
        if setup_sqlite_fallback():
            if test_database_connection():
                print("\n🎉 SQLite setup successful! You can now run your application.")
            else:
                print("\n❌ SQLite setup failed. Please check the error messages above.")
    elif choice == "3":
        print("\n🐳 Docker Setup:")
        print("1. Make sure Docker is installed and running")
        print("2. Run: docker-compose up -d postgres")
        print("3. Wait a few seconds for the database to start")
        print("4. Run this script again to test the connection")
    else:
        print("Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()
