version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: ingres_postgres
    environment:
      POSTGRES_DB: ingres_db
      POSTGRES_USER: ingres_user
      POSTGRES_PASSWORD: ingres_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./setup_database.sql:/docker-entrypoint-initdb.d/setup_database.sql
    restart: unless-stopped

  mongodb:
    image: mongo:7
    container_name: ingres_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped

volumes:
  postgres_data:
  mongodb_data:
